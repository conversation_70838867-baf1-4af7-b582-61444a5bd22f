<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('organizations', function (Blueprint $table) {
            $table->id();
            $table->string('model_type')->default('school')->comment('机构类型model： partner 教育局，school 学校');
            $table->integer('model_id')->comment('外部关联表主键id：学校表schools主键id 或 教育局、代理商表partners主键id（根据type判断对应哪个表）');
            $table->string('org_name')->default('school')->comment('机构名称（用于无状态时，根据名称获取机构配置信息）');
            $table->string('creator', 20)->default('')->comment('添加人');
            $table->string('updater', 20)->default('')->comment('最后更新人');
            $table->timestamps();

            // 添加性能优化索引
            $table->index(['model_type', 'model_id'], 'idx_model_type_id');
            $table->index('model_id');
        });
        \Illuminate\Support\Facades\DB::statement("ALTER TABLE `organizations` comment '虚拟代理机构表（各个主体汇总关系）'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('organizations');
    }
};
