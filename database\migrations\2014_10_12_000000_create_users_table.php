<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('users', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('organization_id')->comment('机构id');
            $table->string('username')->unique()->comment('用户名');// 唯一
            $table->string('real_name',50)->comment('姓名或显示名');
            $table->enum('gender', ['1', '2'])->comment('性别1男2女');
            $table->string('email')->nullable()->unique()->comment('邮箱');
            $table->timestamp('email_verified_at')->nullable();
            $table->string('password')->comment('密码');
            $table->bigInteger('role_id')->nullable()->comment('旧系统角色id（冗余字段，用于对应旧系统角色ID）');
            $table->string('openid')->nullable()->comment('openid');
            $table->string('phone',20)->nullable()->comment('手机号');
            $table->tinyInteger('status')->default(1)->comment('状态1启用2禁用');
            $table->rememberToken();
            $table->timestamps();
            $table->string('creator',20)->nullable()->comment('创建人');
            $table->string('updater',20)->nullable()->comment('最后更新人');

            // 添加性能优化索引
            $table->index('organization_id');
            $table->index('role_id');
            $table->index('status');
            $table->index('openid');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('users');
    }
};
