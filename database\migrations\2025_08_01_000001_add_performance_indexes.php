<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     * 为 StudentSeeder_2 性能优化添加索引
     */
    public function up(): void
    {
        // 为 student_convert 表添加索引（如果表存在）
        if (Schema::connection('mysql_prod')->hasTable('student_convert')) {
            Schema::connection('mysql_prod')->table('student_convert', function (Blueprint $table) {
                // 检查索引是否已存在，避免重复创建
                if (!$this->indexExists('mysql_prod', 'student_convert', 'idx_school_member')) {
                    $table->index(['school_id', 'member_id'], 'idx_school_member');
                }
                if (!$this->indexExists('mysql_prod', 'student_convert', 'school_id')) {
                    $table->index('school_id');
                }
                if (!$this->indexExists('mysql_prod', 'student_convert', 'member_id')) {
                    $table->index('member_id');
                }
            });
        }

        // 为现有表添加缺失的索引
        Schema::table('users', function (Blueprint $table) {
            if (!$this->indexExists('mysql', 'users', 'users_organization_id_index')) {
                $table->index('organization_id');
            }
            if (!$this->indexExists('mysql', 'users', 'users_role_id_index')) {
                $table->index('role_id');
            }
            if (!$this->indexExists('mysql', 'users', 'users_status_index')) {
                $table->index('status');
            }
            if (!$this->indexExists('mysql', 'users', 'users_openid_index')) {
                $table->index('openid');
            }
        });

        Schema::table('students', function (Blueprint $table) {
            if (!$this->indexExists('mysql', 'students', 'students_user_id_index')) {
                $table->index('user_id');
            }
            if (!$this->indexExists('mysql', 'students', 'students_school_id_index')) {
                $table->index('school_id');
            }
            if (!$this->indexExists('mysql', 'students', 'students_school_campus_id_index')) {
                $table->index('school_campus_id');
            }
            if (!$this->indexExists('mysql', 'students', 'students_init_grade_id_index')) {
                $table->index('init_grade_id');
            }
            if (!$this->indexExists('mysql', 'students', 'students_grade_year_index')) {
                $table->index('grade_year');
            }
            if (!$this->indexExists('mysql', 'students', 'idx_school_grade_year')) {
                $table->index(['school_id', 'grade_year'], 'idx_school_grade_year');
            }
            if (!$this->indexExists('mysql', 'students', 'students_student_no_index')) {
                $table->index('student_no');
            }
            if (!$this->indexExists('mysql', 'students', 'students_school_no_index')) {
                $table->index('school_no');
            }
        });

        Schema::table('organizations', function (Blueprint $table) {
            if (!$this->indexExists('mysql', 'organizations', 'idx_model_type_id')) {
                $table->index(['model_type', 'model_id'], 'idx_model_type_id');
            }
            if (!$this->indexExists('mysql', 'organizations', 'organizations_model_id_index')) {
                $table->index('model_id');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // 删除添加的索引
        if (Schema::connection('mysql_prod')->hasTable('student_convert')) {
            Schema::connection('mysql_prod')->table('student_convert', function (Blueprint $table) {
                $table->dropIndex('idx_school_member');
                $table->dropIndex(['school_id']);
                $table->dropIndex(['member_id']);
            });
        }

        Schema::table('users', function (Blueprint $table) {
            $table->dropIndex(['organization_id']);
            $table->dropIndex(['role_id']);
            $table->dropIndex(['status']);
            $table->dropIndex(['openid']);
        });

        Schema::table('students', function (Blueprint $table) {
            $table->dropIndex(['user_id']);
            $table->dropIndex(['school_id']);
            $table->dropIndex(['school_campus_id']);
            $table->dropIndex(['init_grade_id']);
            $table->dropIndex(['grade_year']);
            $table->dropIndex('idx_school_grade_year');
            $table->dropIndex(['student_no']);
            $table->dropIndex(['school_no']);
        });

        Schema::table('organizations', function (Blueprint $table) {
            $table->dropIndex('idx_model_type_id');
            $table->dropIndex(['model_id']);
        });
    }

    /**
     * 检查索引是否存在
     */
    private function indexExists(string $connection, string $table, string $indexName): bool
    {
        $indexes = DB::connection($connection)->select("SHOW INDEX FROM {$table}");
        foreach ($indexes as $index) {
            if ($index->Key_name === $indexName) {
                return true;
            }
        }
        return false;
    }
};
