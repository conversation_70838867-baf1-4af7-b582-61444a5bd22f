<?php

namespace Database\Seeders;

use Illuminate\Database\QueryException;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

/**
 * 学生数据填充 - 优化版本
 * User数据填充
 */
class StudentSeeder_2_Optimized extends Seeder
{
    protected int $school_id;
    protected string $connect = 'mysql_prod';

    public function __construct($schoolId)
    {
        $this->school_id = $schoolId;
        $this->connect = 'mysql_prod';
    }

    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $school_id = $this->school_id;
        
        // 获取学校对应的机构ID - 使用缓存避免重复查询
        $orgID = $this->getOrganizationId($school_id);
        if (!$orgID) {
            echo "学校ID：{$school_id} 未找到对应的机构ID，跳过处理\n";
            return;
        }

        $total = 0;
        $batchNum = 1;
        $batchSize = 2000; // 增加批次大小以减少插入次数

        try {
            // 开启事务以提高性能
            DB::beginTransaction();
            
            echo "学校ID：{$school_id} 开始数据迁移 " . date('Y-m-d H:i:s') . "\n";
            
            // 预先禁用外键检查以提高插入性能
            DB::statement('SET FOREIGN_KEY_CHECKS=0');
            
            // 学生用户数据迁移
            DB::connection($this->connect)->table('student_convert')
                ->where('school_id', $school_id)
                ->orderBy('member_id')
                ->chunk($batchSize, function ($items) use ($orgID, $school_id, $batchSize, &$total, &$batchNum) {
                    $this->processBatch($items, $orgID, $school_id, $batchSize, $total, $batchNum);
                });

            // 重新启用外键检查
            DB::statement('SET FOREIGN_KEY_CHECKS=1');
            
            DB::commit();
            
            echo "学校ID：{$school_id} 数据迁移完成，共处理 {$total} 条数据 " . date('Y-m-d H:i:s') . "\n";

        } catch (QueryException $e) {
            DB::rollBack();
            DB::statement('SET FOREIGN_KEY_CHECKS=1');
            echo "学校ID：{$school_id} 数据迁移失败：" . $e->getMessage() . "\n";
            throw $e;
        }
    }

    /**
     * 获取机构ID（带缓存）
     */
    private function getOrganizationId(int $school_id): ?int
    {
        static $orgCache = [];
        
        if (!isset($orgCache[$school_id])) {
            $orgCache[$school_id] = DB::table('organizations')
                ->where('model_id', $school_id)
                ->where('model_type', 'school')
                ->value('id');
        }
        
        return $orgCache[$school_id];
    }

    /**
     * 处理单个批次
     */
    private function processBatch($items, int $orgID, int $school_id, int $batchSize, int &$total, int &$batchNum): void
    {
        $user_data = [];
        $student_data = [];
        $currentTime = date('Y-m-d H:i:s');

        foreach ($items as $item) {
            // 处理角色ID
            $old_role_id = $this->processRoleId($item->role_id);

            // 用户User信息
            $user_data[] = [
                'id' => $item->member_id,
                'organization_id' => $orgID,
                'username' => $item->username,
                'real_name' => $item->name,
                'gender' => $item->gender > 1 ? 2 : 1,
                'md5_password' => $item->password,
                'role_id' => $old_role_id,
                'creator' => '学生',
                'created_at' => $currentTime,
                'updated_at' => $currentTime,
            ];

            // 处理年级和日期信息
            $gradeInfo = $this->processGradeInfo($item);

            // 学生Student信息
            $student_data[] = [
                'id' => $item->member_id,
                'student_ids' => $item->student_ids,
                'user_id' => $item->member_id,
                'school_id' => $school_id,
                'school_campus_id' => $item->school_district,
                'student_name' => $item->name,
                'gender' => $item->gender > 1 ? 2 : 1,
                'student_no' => $gradeInfo['student_no'],
                'school_no' => $gradeInfo['student_no'],
                'init_grade_id' => $gradeInfo['init_grade_id'],
                'grade_year' => $gradeInfo['grade_year'],
                'date_start' => $gradeInfo['date_start'],
                'date_due' => $gradeInfo['date_due'],
                'created_at' => $currentTime,
                'updated_at' => $currentTime,
            ];

            $total++;
        }

        // 批量插入数据
        if (!empty($user_data)) {
            $this->batchInsert('users', $user_data);
            $this->batchInsert('students', $student_data);
            
            echo "学校ID：{$school_id} 完成第 {$batchNum} 批数据迁移，本批 " . count($user_data) . " 条 " . date('Y-m-d H:i:s') . "\n";
            $batchNum++;
        }
    }

    /**
     * 处理角色ID
     */
    private function processRoleId(?string $role_id): ?string
    {
        if (!$role_id || $role_id === '0,,0') {
            return null;
        }
        return preg_replace('/0,(\d+),0/', '$1', $role_id);
    }

    /**
     * 处理年级信息
     */
    private function processGradeInfo($item): array
    {
        $init_grade_id = $item->grade_sorts ? intval(explode(',', $item->grade_sorts)[0]) : null;
        $grade_year = $item->grade_years ? intval(explode(',', $item->grade_years)[0]) : null;
        $student_no = $item->student_nos ? explode(',', $item->student_nos)[0] : '';
        
        $date_start = null;
        $date_due = null;
        
        if ($grade_year) {
            $date_start = date('Y-m-d', strtotime($grade_year . '-09-01'));
        }
        
        if ($init_grade_id && $grade_year) {
            // 根据年级区间推算毕业日期
            if ($init_grade_id <= 5) {
                $due_year = $grade_year + 5 - $init_grade_id;
            } elseif ($init_grade_id <= 9) {
                $due_year = $grade_year + 9 - $init_grade_id;
            } else {
                $due_year = $grade_year + 12 - $init_grade_id;
            }
            $date_due = date('Y-m-d', strtotime($due_year . '-06-30'));
        }

        return [
            'init_grade_id' => $init_grade_id,
            'grade_year' => $grade_year,
            'student_no' => $student_no,
            'date_start' => $date_start,
            'date_due' => $date_due,
        ];
    }

    /**
     * 优化的批量插入方法
     */
    private function batchInsert(string $table, array $data): void
    {
        if (empty($data)) {
            return;
        }

        try {
            // 使用 INSERT IGNORE 避免主键冲突导致的错误
            $columns = array_keys($data[0]);
            $placeholders = '(' . str_repeat('?,', count($columns) - 1) . '?)';
            $values = [];
            
            foreach ($data as $row) {
                $values = array_merge($values, array_values($row));
            }
            
            $sql = "INSERT IGNORE INTO {$table} (" . implode(',', $columns) . ") VALUES " 
                 . str_repeat($placeholders . ',', count($data) - 1) . $placeholders;
            
            DB::insert($sql, $values);
            
        } catch (QueryException $e) {
            // 如果批量插入失败，尝试逐条插入以找出问题数据
            echo "批量插入 {$table} 失败，尝试逐条插入...\n";
            foreach ($data as $index => $row) {
                try {
                    DB::table($table)->insertOrIgnore($row);
                } catch (QueryException $singleError) {
                    echo "插入第 {$index} 条数据失败：" . $singleError->getMessage() . "\n";
                }
            }
        }
    }
}
